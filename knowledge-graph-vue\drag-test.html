<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>节点拖拽测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://pixijs.download/release/pixi.min.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: #1a1a2e;
        color: #ffffff;
        overflow: hidden;
        height: 100vh;
      }

      .app {
        display: flex;
        height: 100vh;
      }

      .controls {
        width: 300px;
        background: rgba(0, 0, 0, 0.9);
        padding: 20px;
        overflow-y: auto;
      }

      .controls h1 {
        color: #4ecdc4;
        margin-bottom: 20px;
        font-size: 18px;
      }

      .control-group {
        margin-bottom: 20px;
        padding: 15px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        border: 1px solid rgba(78, 205, 196, 0.2);
      }

      .control-group h3 {
        color: #4ecdc4;
        margin-bottom: 10px;
        font-size: 14px;
      }

      .test-info {
        background: rgba(255, 193, 7, 0.1);
        border: 1px solid rgba(255, 193, 7, 0.3);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
      }

      .test-info h3 {
        color: #ffc107;
        margin-bottom: 10px;
      }

      .test-info ul {
        list-style: none;
        padding-left: 0;
      }

      .test-info li {
        margin-bottom: 8px;
        padding-left: 20px;
        position: relative;
      }

      .test-info li:before {
        content: "•";
        color: #ffc107;
        position: absolute;
        left: 0;
      }

      .canvas-container {
        flex: 1;
        position: relative;
        background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f0f23 100%);
      }

      .debug-info {
        position: absolute;
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.8);
        padding: 10px;
        border-radius: 5px;
        font-family: monospace;
        font-size: 12px;
        color: #4ecdc4;
        z-index: 1000;
      }

      button {
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 12px;
        margin: 5px 0;
        width: 100%;
        transition: all 0.3s ease;
      }

      button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="app">
        <div class="controls">
          <h1>🎯 节点拖拽测试</h1>
          
          <div class="test-info">
            <h3>测试说明</h3>
            <ul>
              <li>拖拽任意节点到画布各个位置</li>
              <li>测试缩放后的拖拽精度</li>
              <li>测试平移后的拖拽精度</li>
              <li>观察节点是否完全跟随鼠标</li>
            </ul>
          </div>

          <div class="control-group">
            <h3>视图控制</h3>
            <button @click="resetView">重置视图</button>
            <button @click="zoomIn">放大 (1.2x)</button>
            <button @click="zoomOut">缩小 (0.8x)</button>
          </div>

          <div class="control-group">
            <h3>测试场景</h3>
            <button @click="testScenario1">场景1: 正常拖拽</button>
            <button @click="testScenario2">场景2: 缩放后拖拽</button>
            <button @click="testScenario3">场景3: 平移后拖拽</button>
          </div>

          <div class="control-group">
            <h3>调试信息</h3>
            <div style="font-family: monospace; font-size: 11px; color: #ccc;">
              <div>视口缩放: {{ debugInfo.scale.toFixed(2) }}</div>
              <div>视口位置: ({{ debugInfo.x.toFixed(0) }}, {{ debugInfo.y.toFixed(0) }})</div>
              <div>鼠标位置: ({{ debugInfo.mouseX.toFixed(0) }}, {{ debugInfo.mouseY.toFixed(0) }})</div>
              <div>拖拽状态: {{ debugInfo.isDragging ? '是' : '否' }}</div>
            </div>
          </div>
        </div>

        <div class="canvas-container">
          <div ref="canvasContainer" style="width: 100%; height: 100%;"></div>
          
          <div class="debug-info">
            <div>拖拽测试模式</div>
            <div>节点数量: {{ nodes.length }}</div>
            <div>边数量: {{ edges.length }}</div>
          </div>
        </div>
      </div>
    </div>

    <script>
      const { createApp, ref, onMounted, reactive } = Vue;

      createApp({
        setup() {
          const canvasContainer = ref(null);
          const debugInfo = reactive({
            scale: 1,
            x: 0,
            y: 0,
            mouseX: 0,
            mouseY: 0,
            isDragging: false
          });

          // 创建简单的测试数据
          const nodes = ref([
            { id: 'node1', title: '测试节点1', degree: 3 },
            { id: 'node2', title: '测试节点2', degree: 2 },
            { id: 'node3', title: '测试节点3', degree: 4 },
            { id: 'node4', title: '测试节点4', degree: 1 },
            { id: 'node5', title: '测试节点5', degree: 2 }
          ]);

          const edges = ref([
            { id: 'edge1', source: 'node1', target: 'node2', relationship: '连接' },
            { id: 'edge2', source: 'node2', target: 'node3', relationship: '连接' },
            { id: 'edge3', source: 'node3', target: 'node4', relationship: '连接' },
            { id: 'edge4', source: 'node1', target: 'node5', relationship: '连接' }
          ]);

          let app, simulation, viewport = { x: 0, y: 0, scale: 1 };
          let isDragging = false, dragStart = null, dragTarget = null;

          function initPixi() {
            app = new PIXI.Application({
              width: canvasContainer.value.clientWidth,
              height: canvasContainer.value.clientHeight,
              backgroundColor: 0x0a0a0a,
              antialias: true
            });

            canvasContainer.value.appendChild(app.view);

            // 初始化D3力导向模拟
            simulation = d3.forceSimulation(nodes.value)
              .force('link', d3.forceLink(edges.value).id(d => d.id).distance(100))
              .force('charge', d3.forceManyBody().strength(-300))
              .force('center', d3.forceCenter(app.screen.width / 2, app.screen.height / 2))
              .on('tick', updatePositions);

            // 创建节点图形
            nodes.value.forEach(node => {
              const graphic = new PIXI.Graphics();
              graphic.beginFill(0x4ecdc4);
              graphic.drawCircle(0, 0, 20);
              graphic.endFill();
              graphic.interactive = true;
              graphic.buttonMode = true;
              graphic.nodeData = node;
              
              app.stage.addChild(graphic);
              node.graphic = graphic;
            });

            // 设置事件监听
            app.stage.on('pointerdown', handlePointerDown);
            app.stage.on('pointermove', handlePointerMove);
            app.stage.on('pointerup', handlePointerUp);
            app.stage.on('pointerupoutside', handlePointerUp);

            // 鼠标位置跟踪
            app.stage.on('pointermove', (event) => {
              debugInfo.mouseX = event.data.global.x;
              debugInfo.mouseY = event.data.global.y;
            });

            updateViewTransform();
          }

          function updatePositions() {
            nodes.value.forEach(node => {
              if (node.graphic && typeof node.x === 'number' && typeof node.y === 'number') {
                node.graphic.x = node.x;
                node.graphic.y = node.y;
              }
            });
          }

          function handlePointerDown(event) {
            isDragging = true;
            dragStart = { x: event.data.global.x, y: event.data.global.y };
            dragTarget = event.target;
            debugInfo.isDragging = true;

            if (dragTarget && dragTarget.nodeData) {
              const node = simulation.nodes().find(n => n.id === dragTarget.nodeData.id);
              if (node) {
                node.fx = node.x;
                node.fy = node.y;
                dragTarget.startWorldX = node.x;
                dragTarget.startWorldY = node.y;
              }
            }
          }

          function handlePointerMove(event) {
            if (!isDragging) return;

            const currentPos = { x: event.data.global.x, y: event.data.global.y };
            const deltaX = currentPos.x - dragStart.x;
            const deltaY = currentPos.y - dragStart.y;

            if (dragTarget && dragTarget.nodeData) {
              const node = simulation.nodes().find(n => n.id === dragTarget.nodeData.id);
              if (node && dragTarget.startWorldX !== undefined && dragTarget.startWorldY !== undefined) {
                const worldDeltaX = deltaX / viewport.scale;
                const worldDeltaY = deltaY / viewport.scale;
                
                node.fx = dragTarget.startWorldX + worldDeltaX;
                node.fy = dragTarget.startWorldY + worldDeltaY;
                simulation.alpha(0.1).restart();
              }
            } else {
              viewport.x += deltaX;
              viewport.y += deltaY;
              updateViewTransform();
            }

            dragStart = currentPos;
          }

          function handlePointerUp(event) {
            if (dragTarget && dragTarget.nodeData) {
              const node = simulation.nodes().find(n => n.id === dragTarget.nodeData.id);
              if (node) {
                node.fx = null;
                node.fy = null;
              }
              if (dragTarget.startWorldX !== undefined) {
                delete dragTarget.startWorldX;
                delete dragTarget.startWorldY;
              }
            }

            isDragging = false;
            dragTarget = null;
            debugInfo.isDragging = false;
          }

          function updateViewTransform() {
            if (!app) return;
            app.stage.scale.set(viewport.scale);
            app.stage.position.set(viewport.x, viewport.y);
            
            debugInfo.scale = viewport.scale;
            debugInfo.x = viewport.x;
            debugInfo.y = viewport.y;
          }

          // 控制函数
          function resetView() {
            viewport = { x: 0, y: 0, scale: 1 };
            updateViewTransform();
          }

          function zoomIn() {
            viewport.scale = Math.min(viewport.scale * 1.2, 3);
            updateViewTransform();
          }

          function zoomOut() {
            viewport.scale = Math.max(viewport.scale * 0.8, 0.3);
            updateViewTransform();
          }

          function testScenario1() {
            resetView();
            alert('场景1: 请尝试拖拽节点，观察是否完全跟随鼠标');
          }

          function testScenario2() {
            viewport.scale = 1.5;
            updateViewTransform();
            alert('场景2: 已放大1.5倍，请测试拖拽精度');
          }

          function testScenario3() {
            viewport.x = 100;
            viewport.y = 50;
            viewport.scale = 0.8;
            updateViewTransform();
            alert('场景3: 已平移并缩小，请测试拖拽精度');
          }

          onMounted(() => {
            initPixi();
          });

          return {
            canvasContainer,
            debugInfo,
            nodes,
            edges,
            resetView,
            zoomIn,
            zoomOut,
            testScenario1,
            testScenario2,
            testScenario3
          };
        }
      }).mount('#app');
    </script>
  </body>
</html>
