# 知识图谱边悬停功能开发状态报告

## 问题描述

在知识图谱可视化项目中，需要实现鼠标悬停在边（连线）上时显示边的详细信息面板的功能。类似于节点悬停功能，用户应该能够：

1. 将鼠标悬停在图中的边上
2. 0.5秒后自动显示边的详情面板
3. 鼠标移开后隐藏详情面板
4. 支持缩放和平移后的正确交互

## 目标

- ✅ 实现边的悬停检测
- ✅ 显示边的详情面板（包含 ID、Source、Target、Weight 等信息）
- ⚠️ 支持画布缩放和平移后的正确坐标转换
- ⚠️ 确保在所有视图变换下都能准确检测边的位置

## 之前的现状

### 初始问题
- 边的悬停功能完全不工作
- 鼠标悬停在边上没有任何响应
- 控制台没有相关事件输出

### 根本原因
- **图层遮挡问题**：节点容器（nodesContainer）在边容器（edgesContainer）之上
- 边图形无法接收到鼠标事件，因为被节点图层阻挡
- 传统的 PIXI.js 事件监听器方案在这种图层结构下失效

## 使用的解决方案

### 方案选择：全局鼠标事件检测

放弃了直接在边图形上绑定事件监听器的方案，改为：

1. **全局事件监听**：在整个画布（app.stage）上监听鼠标移动和点击事件
2. **数学计算检测**：通过几何算法判断鼠标位置是否在边上
3. **保持图层结构**：维持原有的视觉层次（边在下，节点在上）

### 核心实现

```javascript
// 全局鼠标事件监听
app.stage.on('pointermove', handleGlobalPointerMove)
app.stage.on('pointerdown', handleGlobalPointerDown)

// 边检测算法
function findEdgeAtPosition(x, y) {
  const threshold = 15 // 检测阈值
  for (const edge of graphData.filteredEdges) {
    const distance = distanceToLineSegment(x, y, sourceX, sourceY, targetX, targetY)
    if (distance <= threshold) {
      return edge
    }
  }
  return null
}

// 点到线段距离计算
function distanceToLineSegment(px, py, x1, y1, x2, y2) {
  // 使用向量投影算法计算最短距离
}
```

### 技术细节

- **检测阈值**：15像素，确保容易悬停到边
- **性能优化**：每100次鼠标移动打印一次调试信息
- **状态管理**：跟踪当前悬停的边，避免重复触发
- **边可视化**：临时将边线加粗至5px并改为青色，便于测试

## 当前状态

### ✅ 已解决的问题
- 边悬停检测基本功能正常
- 页面初始加载时可以正确检测边的悬停
- 详情面板能够正确显示边的信息
- 控制台输出正常的调试信息

### ⚠️ 当前问题：坐标转换不正确

**问题现象**：
- 页面初始加载时：鼠标悬停在边上 → 正确显示边信息 ✅
- 画布缩放/平移后：鼠标悬停在边上 → 无响应 ❌
- 画布缩放/平移后：鼠标在某个偏移位置悬停 → 显示边信息 ❌

**根本原因**：
坐标系统不匹配。全局鼠标事件获取的是屏幕坐标，但边的位置计算使用的是世界坐标，当画布进行缩放或平移变换后，两个坐标系统之间缺少正确的转换。

### 坐标系统分析

1. **鼠标坐标**：`event.global.x/y` - 相对于画布的屏幕坐标
2. **边的坐标**：`sourceNode.x/y, targetNode.y` - 世界坐标系中的位置
3. **画布变换**：`app.stage.position` 和 `app.stage.scale` - 平移和缩放变换

## 下一步计划

### 1. 坐标转换修复（高优先级）

需要在 `findEdgeAtPosition` 函数中添加正确的坐标转换：

```javascript
function findEdgeAtPosition(screenX, screenY) {
  // 将屏幕坐标转换为世界坐标
  const worldX = (screenX - app.stage.position.x) / app.stage.scale.x
  const worldY = (screenY - app.stage.position.y) / app.stage.scale.y
  
  // 使用世界坐标进行边检测
  // ...
}
```

### 2. 测试验证

- 测试初始状态下的边悬停
- 测试缩放后的边悬停
- 测试平移后的边悬停
- 测试组合变换后的边悬停

### 3. 性能优化

- 减少不必要的计算
- 优化边检测算法
- 考虑使用空间索引加速查找

### 4. 用户体验改进

- 恢复边线的正常样式（从调试用的粗青色改回细白色）
- 优化悬停响应时间
- 添加边悬停时的视觉反馈

## 技术栈

- **前端框架**：Vue 3 + Vite
- **图形渲染**：PIXI.js v7
- **力导向布局**：D3.js force simulation
- **状态管理**：Pinia

## 相关文件

- `src/components/GraphCanvas.vue` - 主要实现文件
- `src/components/HoverDetailPanel.vue` - 悬停详情面板
- `src/stores/uiState.js` - UI状态管理

---

**最后更新**：2025-07-31  
**状态**：坐标转换问题待修复  
**优先级**：高
