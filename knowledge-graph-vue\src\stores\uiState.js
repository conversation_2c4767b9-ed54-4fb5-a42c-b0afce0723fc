import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/**
 * UI 状态管理 Store
 * 负责管理界面状态、用户交互和视图控制
 */
export const useUIStateStore = defineStore('uiState', () => {
  // 侧边栏状态
  const sidebarCollapsed = ref(false)
  const sidebarWidth = computed(() => (sidebarCollapsed.value ? 60 : 400))

  // 物理引擎状态
  const physicsRunning = ref(true)
  const physicsAlpha = ref(0.3)

  // 性能设置
  const gpuAcceleration = ref(true)
  const showFPS = ref(true)
  const targetFPS = ref(60)

  // 选中状态
  const selectedNode = ref(null)
  const selectedEdge = ref(null)
  const hoveredNode = ref(null)
  const hoveredEdge = ref(null)

  // 详情面板状态
  const showDetailPanel = ref(false)
  const detailPanelData = ref(null)
  const detailPanelType = ref('node') // 'node' | 'edge'

  // 悬停详情面板状态
  const showHoverDetailPanel = ref(false)
  const hoverDetailPanelData = ref(null)
  const hoverDetailPanelType = ref('node') // 'node' | 'edge'

  // 视图变换状态
  const viewTransform = ref({
    x: 0,
    y: 0,
    scale: 1,
  })

  // 画布尺寸
  const canvasSize = ref({
    width: 800,
    height: 600,
  })

  // 加载状态
  const loading = ref(false)
  const loadingMessage = ref('')

  // 错误状态
  const error = ref(null)

  // 性能监控
  const performance = ref({
    fps: 0,
    frameTime: 0,
    nodeCount: 0,
    edgeCount: 0,
    memoryUsage: 0,
  })

  // 工具提示状态
  const tooltip = ref({
    visible: false,
    x: 0,
    y: 0,
    content: '',
    delay: 500, // 延迟显示时间(ms)
  })

  /**
   * 侧边栏控制
   */
  function toggleSidebar() {
    sidebarCollapsed.value = !sidebarCollapsed.value
    saveState()
  }

  function setSidebarCollapsed(collapsed) {
    sidebarCollapsed.value = collapsed
    saveState()
  }

  /**
   * 物理引擎控制
   */
  function startPhysics() {
    physicsRunning.value = true
  }

  function stopPhysics() {
    physicsRunning.value = false
  }

  function togglePhysics() {
    physicsRunning.value = !physicsRunning.value
  }

  function setPhysicsAlpha(alpha) {
    physicsAlpha.value = alpha
  }

  /**
   * 性能设置
   */
  function toggleGPUAcceleration() {
    gpuAcceleration.value = !gpuAcceleration.value
    saveState()
  }

  function toggleFPSDisplay() {
    showFPS.value = !showFPS.value
    saveState()
  }

  function setTargetFPS(fps) {
    targetFPS.value = fps
    saveState()
  }

  /**
   * 选中状态管理
   */
  function selectNode(node) {
    selectedNode.value = node
    selectedEdge.value = null
    if (node) {
      showNodeDetail(node)
    } else {
      hideDetailPanel()
    }
  }

  function selectEdge(edge) {
    selectedEdge.value = edge
    selectedNode.value = null
    if (edge) {
      showEdgeDetail(edge)
    } else {
      hideDetailPanel()
    }
  }

  function clearSelection() {
    selectedNode.value = null
    selectedEdge.value = null
    hideDetailPanel()
  }

  /**
   * 悬停状态管理
   */
  function hoverNode(node) {
    hoveredNode.value = node
  }

  function hoverEdge(edge) {
    hoveredEdge.value = edge
  }

  function clearHover() {
    hoveredNode.value = null
    hoveredEdge.value = null
  }

  /**
   * 详情面板管理
   */
  function showNodeDetail(node) {
    detailPanelData.value = node
    detailPanelType.value = 'node'
    showDetailPanel.value = true
  }

  function showEdgeDetail(edge) {
    detailPanelData.value = edge
    detailPanelType.value = 'edge'
    showDetailPanel.value = true
  }

  function hideDetailPanel() {
    showDetailPanel.value = false
    detailPanelData.value = null
  }

  /**
   * 悬停详情面板管理
   */
  function showHoverNodeDetail(node) {
    console.log('🔍 uiState.showHoverNodeDetail 被调用:', {
      node,
      beforeState: {
        showHoverDetailPanel: showHoverDetailPanel.value,
        hoverDetailPanelData: hoverDetailPanelData.value,
        hoverDetailPanelType: hoverDetailPanelType.value,
      },
    })

    hoverDetailPanelData.value = node
    hoverDetailPanelType.value = 'node'
    showHoverDetailPanel.value = true

    console.log('✅ uiState.showHoverNodeDetail 完成:', {
      afterState: {
        showHoverDetailPanel: showHoverDetailPanel.value,
        hoverDetailPanelData: hoverDetailPanelData.value,
        hoverDetailPanelType: hoverDetailPanelType.value,
      },
    })
  }

  function showHoverEdgeDetail(edge) {
    hoverDetailPanelData.value = edge
    hoverDetailPanelType.value = 'edge'
    showHoverDetailPanel.value = true
  }

  function hideHoverDetailPanel() {
    showHoverDetailPanel.value = false
    hoverDetailPanelData.value = null
  }

  /**
   * 视图变换管理
   */
  function updateViewTransform(transform) {
    viewTransform.value = { ...viewTransform.value, ...transform }
  }

  function resetViewTransform() {
    viewTransform.value = { x: 0, y: 0, scale: 1 }
  }

  function zoomIn(factor = 1.1) {
    const newScale = Math.min(viewTransform.value.scale * factor, 5.0)
    viewTransform.value.scale = newScale
  }

  function zoomOut(factor = 0.9) {
    const newScale = Math.max(viewTransform.value.scale * factor, 0.1)
    viewTransform.value.scale = newScale
  }

  /**
   * 画布尺寸管理
   */
  function updateCanvasSize(width, height) {
    canvasSize.value = { width, height }
  }

  /**
   * 加载状态管理
   */
  function setLoading(isLoading, message = '') {
    loading.value = isLoading
    loadingMessage.value = message
  }

  /**
   * 错误状态管理
   */
  function setError(errorMessage) {
    error.value = errorMessage
  }

  function clearError() {
    error.value = null
  }

  /**
   * 性能监控更新
   */
  function updatePerformance(perfData) {
    performance.value = { ...performance.value, ...perfData }
  }

  /**
   * 工具提示管理
   */
  function showTooltip(x, y, content) {
    tooltip.value = {
      visible: true,
      x,
      y,
      content,
    }
  }

  function hideTooltip() {
    tooltip.value.visible = false
  }

  /**
   * 从 localStorage 恢复状态
   */
  function restoreState() {
    try {
      const saved = localStorage.getItem('knowledge-graph-ui-state')
      if (saved) {
        const state = JSON.parse(saved)
        sidebarCollapsed.value = state.sidebarCollapsed ?? false
        gpuAcceleration.value = state.gpuAcceleration ?? true
        showFPS.value = state.showFPS ?? true
        targetFPS.value = state.targetFPS ?? 60

        // 恢复视图变换（如果有的话）
        if (state.viewTransform) {
          viewTransform.value = { ...viewTransform.value, ...state.viewTransform }
        }

        // 恢复画布大小（如果有的话）
        if (state.canvasSize) {
          canvasSize.value = { ...canvasSize.value, ...state.canvasSize }
        }
      }
    } catch (error) {
      console.warn('恢复UI状态失败:', error)
    }
  }

  /**
   * 保存状态到 localStorage
   */
  function saveState() {
    try {
      const state = {
        sidebarCollapsed: sidebarCollapsed.value,
        gpuAcceleration: gpuAcceleration.value,
        showFPS: showFPS.value,
        targetFPS: targetFPS.value,
        viewTransform: viewTransform.value,
        canvasSize: canvasSize.value,
      }
      localStorage.setItem('knowledge-graph-ui-state', JSON.stringify(state))
    } catch (error) {
      console.warn('保存UI状态失败:', error)
    }
  }

  return {
    // 状态
    sidebarCollapsed,
    sidebarWidth,
    physicsRunning,
    physicsAlpha,
    gpuAcceleration,
    showFPS,
    targetFPS,
    selectedNode,
    selectedEdge,
    hoveredNode,
    hoveredEdge,
    showDetailPanel,
    detailPanelData,
    detailPanelType,
    showHoverDetailPanel,
    hoverDetailPanelData,
    hoverDetailPanelType,
    viewTransform,
    canvasSize,
    loading,
    loadingMessage,
    error,
    performance,
    tooltip,

    // 方法
    toggleSidebar,
    setSidebarCollapsed,
    startPhysics,
    stopPhysics,
    togglePhysics,
    setPhysicsAlpha,
    toggleGPUAcceleration,
    toggleFPSDisplay,
    setTargetFPS,
    selectNode,
    selectEdge,
    clearSelection,
    hoverNode,
    hoverEdge,
    clearHover,
    showNodeDetail,
    showEdgeDetail,
    hideDetailPanel,
    showHoverNodeDetail,
    showHoverEdgeDetail,
    hideHoverDetailPanel,
    updateViewTransform,
    resetViewTransform,
    zoomIn,
    zoomOut,
    updateCanvasSize,
    setLoading,
    setError,
    clearError,
    updatePerformance,
    showTooltip,
    hideTooltip,
    restoreState,
    saveState,
  }
})
