<template>
  <div class="performance-panel">
    <div class="panel-header">
      <span class="panel-title">性能监控</span>
      <button 
        class="close-btn"
        @click="uiState.toggleFPSDisplay"
        title="关闭性能面板"
      >
        ✕
      </button>
    </div>
    
    <div class="metrics-list">
      <div class="metric-item">
        <span class="metric-label">FPS:</span>
        <span class="metric-value" :class="getFPSClass(uiState.performance.fps)">
          {{ uiState.performance.fps.toFixed(0) }}
        </span>
      </div>
      
      <div class="metric-item">
        <span class="metric-label">帧时间:</span>
        <span class="metric-value">
          {{ uiState.performance.frameTime.toFixed(1) }}ms
        </span>
      </div>
      
      <div class="metric-item">
        <span class="metric-label">节点:</span>
        <span class="metric-value">
          {{ uiState.performance.nodeCount }}
        </span>
      </div>
      
      <div class="metric-item">
        <span class="metric-label">边:</span>
        <span class="metric-value">
          {{ uiState.performance.edgeCount }}
        </span>
      </div>
      
      <div class="metric-item">
        <span class="metric-label">内存:</span>
        <span class="metric-value">
          {{ formatMemory(uiState.performance.memoryUsage) }}
        </span>
      </div>
    </div>
    
    <!-- FPS 图表 -->
    <div class="fps-chart">
      <canvas 
        ref="fpsCanvas"
        width="200"
        height="60"
        class="fps-canvas"
      ></canvas>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useUIStateStore } from '../stores/uiState.js'

const uiState = useUIStateStore()

// FPS 图表相关
const fpsCanvas = ref(null)
const fpsHistory = ref([])
const maxHistoryLength = 100

/**
 * 获取 FPS 状态类名
 */
function getFPSClass(fps) {
  if (fps >= 50) return 'fps-good'
  if (fps >= 30) return 'fps-medium'
  return 'fps-poor'
}

/**
 * 格式化内存使用量
 */
function formatMemory(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

/**
 * 绘制 FPS 图表
 */
function drawFPSChart() {
  if (!fpsCanvas.value) return
  
  const canvas = fpsCanvas.value
  const ctx = canvas.getContext('2d')
  const width = canvas.width
  const height = canvas.height
  
  // 清除画布
  ctx.clearRect(0, 0, width, height)
  
  if (fpsHistory.value.length < 2) return
  
  // 设置样式
  ctx.strokeStyle = '#4ECDC4'
  ctx.lineWidth = 2
  ctx.lineCap = 'round'
  ctx.lineJoin = 'round'
  
  // 计算缩放
  const maxFPS = Math.max(60, Math.max(...fpsHistory.value))
  const minFPS = Math.min(0, Math.min(...fpsHistory.value))
  const fpsRange = maxFPS - minFPS
  
  // 绘制网格线
  ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)'
  ctx.lineWidth = 1
  
  // 水平网格线
  for (let i = 0; i <= 3; i++) {
    const y = (height / 3) * i
    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(width, y)
    ctx.stroke()
  }
  
  // 绘制 FPS 曲线
  ctx.strokeStyle = '#4ECDC4'
  ctx.lineWidth = 2
  ctx.beginPath()
  
  fpsHistory.value.forEach((fps, index) => {
    const x = (index / (fpsHistory.value.length - 1)) * width
    const y = height - ((fps - minFPS) / fpsRange) * height
    
    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })
  
  ctx.stroke()
  
  // 绘制当前 FPS 点
  if (fpsHistory.value.length > 0) {
    const currentFPS = fpsHistory.value[fpsHistory.value.length - 1]
    const x = width
    const y = height - ((currentFPS - minFPS) / fpsRange) * height
    
    ctx.fillStyle = getFPSColor(currentFPS)
    ctx.beginPath()
    ctx.arc(x - 2, y, 3, 0, Math.PI * 2)
    ctx.fill()
  }
  
  // 绘制目标 FPS 线
  const targetY = height - ((uiState.targetFPS - minFPS) / fpsRange) * height
  ctx.strokeStyle = 'rgba(255, 234, 167, 0.6)'
  ctx.lineWidth = 1
  ctx.setLineDash([5, 5])
  ctx.beginPath()
  ctx.moveTo(0, targetY)
  ctx.lineTo(width, targetY)
  ctx.stroke()
  ctx.setLineDash([])
}

/**
 * 获取 FPS 对应的颜色
 */
function getFPSColor(fps) {
  if (fps >= 50) return '#4ECDC4'
  if (fps >= 30) return '#FFEAA7'
  return '#FF6B6B'
}

/**
 * 更新性能数据
 */
function updatePerformanceData() {
  // 模拟性能数据更新
  const now = performance.now()
  const fps = Math.random() * 20 + 40 // 模拟 40-60 FPS
  const frameTime = 1000 / fps
  
  // 更新 FPS 历史
  fpsHistory.value.push(fps)
  if (fpsHistory.value.length > maxHistoryLength) {
    fpsHistory.value.shift()
  }
  
  // 更新性能数据
  uiState.updatePerformance({
    fps,
    frameTime,
    nodeCount: uiState.performance.nodeCount,
    edgeCount: uiState.performance.edgeCount,
    memoryUsage: performance.memory ? performance.memory.usedJSHeapSize : 0
  })
  
  // 重绘图表
  drawFPSChart()
}

// 性能监控定时器
let performanceTimer = null

/**
 * 启动性能监控
 */
function startPerformanceMonitoring() {
  performanceTimer = setInterval(updatePerformanceData, 100) // 每100ms更新一次
}

/**
 * 停止性能监控
 */
function stopPerformanceMonitoring() {
  if (performanceTimer) {
    clearInterval(performanceTimer)
    performanceTimer = null
  }
}

// 监听显示状态变化
watch(() => uiState.showFPS, (show) => {
  if (show) {
    startPerformanceMonitoring()
  } else {
    stopPerformanceMonitoring()
  }
})

onMounted(() => {
  if (uiState.showFPS) {
    startPerformanceMonitoring()
  }
})

onUnmounted(() => {
  stopPerformanceMonitoring()
})
</script>

<style scoped>
.performance-panel {
  position: fixed;
  top: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(78, 205, 196, 0.3);
  border-radius: 8px;
  padding: 12px;
  min-width: 220px;
  backdrop-filter: blur(10px);
  z-index: 200;
  font-family: 'Courier New', monospace;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(78, 205, 196, 0.2);
}

.panel-title {
  font-size: 12px;
  font-weight: 600;
  color: #4ecdc4;
}

.close-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
}

.metrics-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.metric-label {
  color: rgba(255, 255, 255, 0.8);
}

.metric-value {
  color: #ffffff;
  font-weight: 600;
  text-align: right;
  min-width: 50px;
}

.metric-value.fps-good {
  color: #4ecdc4;
}

.metric-value.fps-medium {
  color: #ffeaa7;
}

.metric-value.fps-poor {
  color: #ff6b6b;
}

.fps-chart {
  border-top: 1px solid rgba(78, 205, 196, 0.2);
  padding-top: 8px;
}

.fps-canvas {
  width: 100%;
  height: 60px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.02);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .performance-panel {
    top: 8px;
    right: 8px;
    min-width: 180px;
    padding: 8px;
  }
  
  .panel-title {
    font-size: 11px;
  }
  
  .metric-item {
    font-size: 10px;
  }
  
  .fps-canvas {
    height: 40px;
  }
}

/* 面板动画 */
.performance-panel {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
